const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../config/config.env') });

// 连接数据库
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/portfolio', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// 删除管理员用户
const deleteAdmin = async () => {
  try {
    // 检查User模型是否已经定义
    let User;
    try {
      User = mongoose.model('User');
    } catch (error) {
      // 如果模型未定义，则导入并定义
      const UserSchema = new mongoose.Schema({
        username: {
          type: String,
          required: [true, '请提供用户名'],
          unique: true,
          trim: true,
          maxlength: [50, '用户名不能超过50个字符']
        },
        email: {
          type: String,
          required: [true, '请提供电子邮件'],
          unique: true,
          match: [
            /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
            '请提供有效的电子邮件'
          ]
        },
        password: {
          type: String,
          required: [true, '请提供密码'],
          minlength: [6, '密码至少需要6个字符'],
          select: false
        },
        role: {
          type: String,
          enum: ['admin', 'editor'],
          default: 'editor'
        },
        resetPasswordToken: String,
        resetPasswordExpire: Date,
        createdAt: {
          type: Date,
          default: Date.now
        },
        updatedAt: {
          type: Date,
          default: Date.now
        }
      });
      
      User = mongoose.model('User', UserSchema);
    }

    // 删除管理员用户
    const result = await User.deleteOne({ email: '<EMAIL>' });
    
    if (result.deletedCount > 0) {
      console.log('管理员用户已删除!');
    } else {
      console.log('未找到管理员用户!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('删除管理员用户时出错:', error);
    process.exit(1);
  }
};

deleteAdmin();