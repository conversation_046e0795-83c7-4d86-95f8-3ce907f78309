import Link from 'next/link';
import { useState, useEffect } from 'react';

const Hero = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);

    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* 动态背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        {/* 动态渐变球 */}
        <div
          className="absolute w-96 h-96 bg-gradient-to-r from-primary-400/30 to-secondary-400/30 rounded-full blur-3xl animate-float"
          style={{
            left: `${20 + mousePosition.x * 0.02}%`,
            top: `${10 + mousePosition.y * 0.02}%`,
          }}
        />
        <div
          className="absolute w-80 h-80 bg-gradient-to-r from-accent-400/20 to-primary-400/20 rounded-full blur-3xl animate-float"
          style={{
            right: `${15 + mousePosition.x * 0.015}%`,
            bottom: `${20 + mousePosition.y * 0.015}%`,
            animationDelay: '2s',
          }}
        />
        <div
          className="absolute w-64 h-64 bg-gradient-to-r from-secondary-400/25 to-accent-400/25 rounded-full blur-3xl animate-float"
          style={{
            left: `${60 + mousePosition.x * 0.01}%`,
            top: `${60 + mousePosition.y * 0.01}%`,
            animationDelay: '4s',
          }}
        />

        {/* 网格背景 */}
        <div className="absolute inset-0 bg-grid opacity-30" />
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 container-custom">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12 lg:gap-16">
          {/* 左侧内容 */}
          <div className={`lg:w-1/2 text-center lg:text-left transition-all duration-1000 ${isVisible ? 'animate-slide-up' : 'opacity-0 translate-y-8'}`}>
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-700 rounded-full text-sm font-semibold mb-4 animate-bounce-gentle">
                ✨ 欢迎来到我的学习空间
              </span>
            </div>

            <h1 className="text-gradient mb-6 leading-tight">
              在校学生 &
              <br />
              <span className="text-gradient-secondary">编程学习者</span>
            </h1>

            <p className="text-xl md:text-2xl text-neutral-600 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0">
              我正在学习
              <span className="text-gradient-primary font-semibold"> Web开发技术 </span>
              ，希望通过这个网站分享我的学习成果和编程心得。
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
              <Link href="/projects" className="btn btn-primary group">
                查看学习项目
                <svg className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
              <Link href="/contact" className="btn btn-outline group">
                联系我
                <svg className="w-5 h-5 ml-2 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </Link>
            </div>

            {/* 技能标签 */}
            <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
              {['React', 'Next.js', 'Node.js', 'TypeScript', 'Tailwind CSS', 'Java', 'Vue.js', 'Spring Boot', 'MySQL', 'Redis'].map((skill, index) => (
                <span
                  key={skill}
                  className="px-4 py-2 bg-white/80 backdrop-blur-sm text-neutral-700 rounded-full text-sm font-medium border border-neutral-200 hover:border-primary-300 hover:text-primary-700 transition-all duration-300 hover:scale-105"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>

          {/* 右侧视觉元素 */}
          <div className={`lg:w-1/2 flex justify-center transition-all duration-1000 delay-300 ${isVisible ? 'animate-scale-in' : 'opacity-0 scale-90'}`}>
            <div className="relative">
              {/* 主要头像容器 */}
              <div className="relative w-80 h-80 md:w-96 md:h-96">
                {/* 外层发光环 */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 rounded-full animate-spin-slow opacity-75 blur-sm" style={{ animationDuration: '20s' }} />

                {/* 中层玻璃效果 */}
                <div className="absolute inset-2 glass-strong rounded-full flex items-center justify-center">
                  {/* 内层渐变背景 */}
                  <div className="absolute inset-4 bg-gradient-to-br from-primary-500/20 via-secondary-500/20 to-accent-500/20 rounded-full animate-gradient-xy" />

                  {/* 头像/图标 */}
                  <div className="relative z-10 text-8xl md:text-9xl animate-bounce-gentle">
                    👨‍💻
                  </div>
                </div>

                {/* 装饰性浮动元素 */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full animate-bounce-gentle" style={{ animationDelay: '1s' }} />
                <div className="absolute -bottom-6 -left-6 w-6 h-6 bg-gradient-to-r from-accent-400 to-primary-400 rounded-full animate-bounce-gentle" style={{ animationDelay: '2s' }} />
                <div className="absolute top-1/4 -left-8 w-4 h-4 bg-gradient-to-r from-secondary-400 to-accent-400 rounded-full animate-bounce-gentle" style={{ animationDelay: '3s' }} />
              </div>

              {/* 代码片段装饰 */}
              <div className="absolute -left-12 top-12 card-glass p-4 rounded-xl transform -rotate-12 animate-float">
                <div className="font-mono text-xs text-primary-600">
                  <div className="text-secondary-500">const</div>
                  <div className="text-accent-500">developer</div>
                  <div className="text-primary-500">= "creative"</div>
                </div>
              </div>

              <div className="absolute -right-16 bottom-16 card-glass p-4 rounded-xl transform rotate-12 animate-float" style={{ animationDelay: '2s' }}>
                <div className="font-mono text-xs text-primary-600">
                  <div className="text-secondary-500">{"<Design />"}</div>
                  <div className="text-accent-500">{"<Code />"}</div>
                  <div className="text-primary-500">{"<Deploy />"}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 滚动指示器 */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-gentle">
        <div className="w-6 h-10 border-2 border-primary-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-primary-400 rounded-full mt-2 animate-bounce" />
        </div>
      </div>
    </div>
  );
};

export default Hero;