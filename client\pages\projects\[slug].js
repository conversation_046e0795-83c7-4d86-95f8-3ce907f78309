import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Image from 'next/image';
import axios from 'axios';
import ReactMarkdown from 'react-markdown';
import ProjectCard from '../../components/projects/ProjectCard';

export default function ProjectDetail() {
  const router = useRouter();
  const { slug } = router.query;

  const [project, setProject] = useState(null);
  const [relatedProjects, setRelatedProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeImage, setActiveImage] = useState(0);

  useEffect(() => {
    const fetchProject = async () => {
      if (!slug) return;

      try {
        setLoading(true);
        const res = await axios.get(`/api/projects/${slug}`);
        setProject(res.data.data);
        setError(null);

        // 获取相关项目（同一分类的其他项目）
        if (res.data.data.category) {
          const relatedRes = await axios.get(
            `/api/projects/category/${res.data.data.category._id}`
          );
          // 过滤掉当前项目
          const filtered = relatedRes.data.data.filter(
            (p) => p._id !== res.data.data._id
          );
          // 只取最多3个相关项目
          setRelatedProjects(filtered.slice(0, 3));
        }
      } catch (err) {
        console.error('获取项目详情错误:', err);
        setError('获取项目详情失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [slug]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center text-red-500">{error}</div>
      </div>
    );
  }

  if (!project) {
    return null;
  }

  // 构建图片数组，包括封面图和其他图片，去重并过滤空值
  const allImages = [
    ...(project.coverImage ? [project.coverImage] : []),
    ...(project.images || [])
  ].filter(Boolean).filter((img, index, arr) => arr.indexOf(img) === index); // 去重

  return (
    <div>
      <Head>
        <title>{project.title} | 项目作品集</title>
        <meta name="description" content={project.description} />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="py-12">
        <div className="container mx-auto px-4">
          {/* 项目标题和分类 */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">{project.title}</h1>
            {project.category && (
              <div className="inline-block bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                {project.category.name}
              </div>
            )}
          </div>

          {/* 项目图片展示 */}
          <div className="mb-12">
            <div className="relative w-full h-96 mb-4 rounded-lg overflow-hidden bg-gray-100">
              {allImages.length > 0 ? (
                <>
                  <Image
                    src={`/uploads/${allImages[activeImage]}`}
                    alt={project.title}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                    style={{ objectFit: 'contain' }}
                    className="rounded-lg"
                  />

                  {/* 左右切换按钮 */}
                  {allImages.length > 1 && (
                    <>
                      <button
                        onClick={() => setActiveImage(activeImage === 0 ? allImages.length - 1 : activeImage - 1)}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10"
                      >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                      <button
                        onClick={() => setActiveImage(activeImage === allImages.length - 1 ? 0 : activeImage + 1)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10"
                      >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </>
                  )}

                  {/* 图片计数器 */}
                  {allImages.length > 1 && (
                    <div className="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                      {activeImage + 1} / {allImages.length}
                    </div>
                  )}
                </>
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <svg className="h-16 w-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
                    </svg>
                    <span className="text-lg">暂无图片</span>
                  </div>
                </div>
              )}
            </div>

            {/* 缩略图导航 */}
            {allImages.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {allImages.map((image, index) => (
                  <div
                    key={index}
                    className={`relative w-24 h-24 rounded cursor-pointer transition-all duration-200 ${
                      index === activeImage
                        ? 'ring-2 ring-primary-500 ring-offset-2'
                        : 'hover:ring-2 hover:ring-gray-300'
                    }`}
                    onClick={() => setActiveImage(index)}
                  >
                    <Image
                      src={`/uploads/${image}`}
                      alt={`${project.title} - 图片 ${index + 1}`}
                      fill
                      sizes="96px"
                      style={{ objectFit: 'cover' }}
                      className="rounded"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 项目信息 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">项目描述</h2>
              <div className="prose max-w-none">
                <ReactMarkdown>{project.content}</ReactMarkdown>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-bold text-gray-900 mb-4">项目详情</h2>
              
              <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">技术栈</h3>
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {(project.demoUrl || project.sourceCodeUrl) && (
                <div className="mb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">链接</h3>
                  <div className="space-y-2">
                    {project.demoUrl && (
                      <a
                        href={project.demoUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-primary-600 hover:text-primary-800"
                      >
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                        </svg>
                        查看演示
                      </a>
                    )}
                    {project.sourceCodeUrl && (
                      <a
                        href={project.sourceCodeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-primary-600 hover:text-primary-800"
                      >
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        查看源代码
                      </a>
                    )}
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">创建时间</h3>
                <p className="text-gray-600">
                  {new Date(project.createdAt).toLocaleDateString('zh-CN')}
                </p>
              </div>
            </div>
          </div>

          {/* 相关项目 */}
          {relatedProjects.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">相关项目</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {relatedProjects.map((relatedProject) => (
                  <ProjectCard key={relatedProject._id} project={relatedProject} />
                ))}
              </div>
            </div>
          )}

          {/* 返回按钮 */}
          <div className="mt-12 text-center">
            <button
              onClick={() => router.push('/projects')}
              className="btn btn-outline"
            >
              返回所有项目
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}