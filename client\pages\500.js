import Link from 'next/link';
import Layout from '../components/layouts/Layout';

export default function Custom500() {
  return (
    <Layout title="服务器错误">
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 text-center">
            <h1 className="text-9xl font-bold text-primary-600">500</h1>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">服务器错误</h2>
            <p className="mt-2 text-sm text-gray-600">
              抱歉，服务器遇到了一个错误。请稍后再试。
            </p>
            <div className="mt-6">
              <Link href="/">
                <a className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  返回首页
                </a>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}