const mongoose = require('mongoose');

const ProjectSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, '请提供项目标题'],
    trim: true,
    maxlength: [100, '标题不能超过100个字符']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  description: {
    type: String,
    required: [true, '请提供项目描述'],
    maxlength: [500, '描述不能超过500个字符']
  },
  content: {
    type: String,
    required: [true, '请提供项目详细内容']
  },
  coverImage: {
    type: String
  },
  images: [
    {
      type: String
    }
  ],
  technologies: [
    {
      type: String,
      required: true
    }
  ],
  demoUrl: {
    type: String
  },
  sourceCodeUrl: {
    type: String
  },
  featured: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['published', 'draft'],
    default: 'draft'
  },
  category: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 创建项目slug
ProjectSchema.pre('save', function(next) {
  if (!this.isModified('title')) {
    next();
    return;
  }
  
  // 创建slug - 将标题转换为URL友好的格式
  this.slug = this.title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除非单词/空格/连字符字符
    .replace(/[\s_-]+/g, '-') // 将空格和下划线替换为连字符
    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符
  
  next();
});

// 更新时间戳
ProjectSchema.pre('findOneAndUpdate', function() {
  this.set({ updatedAt: Date.now() });
});

module.exports = mongoose.model('Project', ProjectSchema);