import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import axios from 'axios';
import AdminLayout from '../../../components/layouts/AdminLayout';

// 图片缩略图组件
const ImageThumbnail = ({ filename, size = 'w-12 h-12' }) => {
  const [imageError, setImageError] = useState(false);

  const imageUrl = `http://localhost:5000/uploads/${filename}`;

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageClick = () => {
    window.open(imageUrl, '_blank');
  };

  if (imageError) {
    return (
      <svg className={`${size} text-primary-500`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={filename}
      className={`${size} object-cover rounded cursor-pointer hover:opacity-80 transition-opacity`}
      onError={handleImageError}
      onClick={handleImageClick}
      title="点击查看原图"
    />
  );
};

export default function AdminUploads({ user }) {
  const router = useRouter();
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState(null);
  const [success, setSuccess] = useState('');

  // 如果用户未登录，重定向到登录页面
  useEffect(() => {
    // 暂时注释掉重定向逻辑来测试
    // if (!user) {
    //   router.push('/login');
    // } else {
      fetchFiles();
    // }
  }, [user, router]);

  // 获取上传的文件列表
  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/upload');
      setFiles(response.data.data || []); // 从response.data.data获取文件数组
      setError('');
    } catch (err) {
      console.error('获取文件列表失败:', err);
      setError('获取文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理文件上传
  const handleFileUpload = async (event) => {
    const selectedFiles = Array.from(event.target.files);
    if (selectedFiles.length === 0) return;

    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    for (const file of selectedFiles) {
      if (!allowedTypes.includes(file.type)) {
        setUploadError(`文件 ${file.name} 格式不支持。请上传 JPG、PNG、GIF 或 WEBP 格式的图片。`);
        return;
      }
      if (file.size > maxSize) {
        setUploadError(`文件 ${file.name} 大小超过 5MB 限制。`);
        return;
      }
    }

    // 上传文件
    const formData = new FormData();
    selectedFiles.forEach(file => {
      formData.append('files', file);
    });

    try {
      setUploadProgress(0);
      setUploadError('');
      
      const response = await axios.post('http://localhost:5000/api/upload/multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        },
      });

      setSuccess(`成功上传 ${selectedFiles.length} 个文件`);
      setUploadProgress(0);
      fetchFiles();
      
      setTimeout(() => {
        setSuccess('');
      }, 3000);
      
      event.target.value = '';
    } catch (err) {
      console.error('文件上传失败:', err);
      setUploadError(err.response?.data?.message || '文件上传失败');
      setUploadProgress(0);
    }
  };

  // 删除文件
  const handleDelete = async (filename) => {
    try {
      await axios.delete(`http://localhost:5000/api/upload/${filename}`);
      setSuccess('文件删除成功');
      setDeleteConfirm(null);
      fetchFiles();
      
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err) {
      console.error('删除文件失败:', err);
      setError(err.response?.data?.message || '删除文件失败');
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件类型图标或缩略图
  const getFileIcon = (filename) => {
    const extension = filename.split('.').pop().toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];

    if (imageExtensions.includes(extension)) {
      return <ImageThumbnail filename={filename} size="w-12 h-12 sm:w-16 sm:h-16" />;
    }

    return (
      <svg className="h-12 w-12 sm:h-16 sm:w-16 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    );
  };

  if (!user) {
    return null;
  }

  return (
    <AdminLayout user={user} title="文件上传管理">
      <Head>
        <title>文件上传管理 | 项目作品集管理系统</title>
      </Head>

      <div className="py-4 sm:py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 页面标题 */}
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-semibold text-gray-900">文件上传管理</h1>
              <p className="mt-1 text-sm text-gray-500">管理您的项目文件和图片资源</p>
            </div>
          </div>

          {/* 上传文件区域 */}
          <div className="mt-4 sm:mt-6 bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="space-y-6 lg:grid lg:grid-cols-3 lg:gap-6 lg:space-y-0">
                <div className="lg:col-span-1">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">上传新文件</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    支持单个或批量上传图片文件
                  </p>
                  <div className="mt-3 space-y-1 text-xs text-gray-400">
                    <div className="flex items-center">
                      <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3" />
                      </svg>
                      支持格式：JPG、PNG、GIF、WEBP
                    </div>
                    <div className="flex items-center">
                      <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3" />
                      </svg>
                      单文件最大：5MB
                    </div>
                    <div className="flex items-center">
                      <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3" />
                      </svg>
                      可同时选择多个文件
                    </div>
                  </div>
                </div>
                <div className="lg:col-span-2">
                  {/* 成功消息 */}
                  {success && (
                    <div className="rounded-md bg-green-50 p-4 mb-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-green-800">{success}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 错误消息 */}
                  {uploadError && (
                    <div className="rounded-md bg-red-50 p-4 mb-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-red-800">{uploadError}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 上传区域 */}
                  <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-primary-400 transition-colors">
                    <div className="space-y-1 text-center">
                      <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                      <div className="flex text-sm text-gray-600">
                        <label htmlFor="file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                          <span>上传文件</span>
                          <input id="file-upload" name="file-upload" type="file" className="sr-only" multiple accept="image/*" onChange={handleFileUpload} />
                        </label>
                        <p className="pl-1 hidden sm:inline">或拖拽文件到此处</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, GIF, WEBP 最大 5MB
                      </p>
                    </div>
                  </div>

                  {/* 上传进度 */}
                  {uploadProgress > 0 && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>上传进度</span>
                        <span>{uploadProgress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-primary-600 h-2 rounded-full transition-all duration-300" style={{ width: `${uploadProgress}%` }}></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 文件列表 */}
          <div className="mt-6 sm:mt-8 bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg leading-6 font-medium text-gray-900">已上传文件</h3>
                  <p className="mt-1 text-sm text-gray-500">管理您的文件资源</p>
                </div>
                {files.length > 0 && (
                  <div className="text-sm text-gray-500">
                    共 {files.length} 个文件
                  </div>
                )}
              </div>
            </div>

            {loading ? (
              <div className="px-4 py-12 sm:px-6 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">加载中...</p>
              </div>
            ) : error ? (
              <div className="px-4 py-12 sm:px-6 text-center text-red-500">
                <svg className="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-red-900">加载失败</h3>
                <p className="mt-1 text-sm text-red-500">{error}</p>
              </div>
            ) : files.length === 0 ? (
              <div className="px-4 py-12 sm:px-6 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">暂无文件</h3>
                <p className="mt-1 text-sm text-gray-500">开始上传您的第一个文件</p>
              </div>
            ) : (
              <>
                {/* 桌面端网格视图 */}
                <div className="hidden sm:block">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6">
                    {files.map((file, index) => (
                      <div key={index} className="relative group bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                        <div className="flex flex-col items-center">
                          <div className="mb-3">
                            {getFileIcon(file.name)}
                          </div>
                          <div className="w-full text-center">
                            <h4 className="text-sm font-medium text-gray-900 truncate" title={file.name}>
                              {file.name}
                            </h4>
                            <p className="text-xs text-gray-500 mt-1">
                              {formatFileSize(file.size)}
                            </p>
                            <p className="text-xs text-gray-400 mt-1">
                              {new Date(file.lastModified).toLocaleDateString('zh-CN')}
                            </p>
                          </div>
                          <div className="flex space-x-2 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                            <a
                              href={`http://localhost:5000/uploads/${file.name}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                            >
                              <svg className="mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                              查看
                            </a>
                            <button
                              onClick={() => setDeleteConfirm(file.name)}
                              className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                              <svg className="mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                              删除
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 移动端列表视图 */}
                <div className="sm:hidden">
                  <ul className="divide-y divide-gray-200">
                    {files.map((file, index) => (
                      <li key={index} className="px-4 py-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            {getFileIcon(file.name)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <div className="mt-1 flex items-center space-x-2 text-xs text-gray-500">
                              <span>{formatFileSize(file.size)}</span>
                              <span>•</span>
                              <span>{new Date(file.lastModified).toLocaleDateString('zh-CN')}</span>
                            </div>
                          </div>
                          <div className="flex flex-col space-y-2">
                            <a
                              href={`http://localhost:5000/uploads/${file.name}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200"
                            >
                              查看
                            </a>
                            <button
                              onClick={() => setDeleteConfirm(file.name)}
                              className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200"
                            >
                              删除
                            </button>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      {deleteConfirm && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">删除文件</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        您确定要删除文件 "{deleteConfirm}" 吗？此操作无法撤销。如果有项目使用了此文件，可能会导致图片显示异常。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => handleDelete(deleteConfirm)}
                >
                  删除
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setDeleteConfirm(null)}
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
