export default function FormCheckbox({
  label,
  name,
  checked,
  onChange,
  error = '',
  disabled = false,
  className = '',
  ...rest
}) {
  return (
    <div className={`mb-4 ${className}`}>
      <div className="flex items-center">
        <input
          type="checkbox"
          id={name}
          name={name}
          checked={checked}
          onChange={onChange}
          disabled={disabled}
          className={`h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded ${disabled ? 'opacity-60 cursor-not-allowed' : ''}`}
          {...rest}
        />
        {label && (
          <label htmlFor={name} className={`ml-2 block text-sm text-gray-700 ${disabled ? 'opacity-60 cursor-not-allowed' : ''}`}>
            {label}
          </label>
        )}
      </div>
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
}