@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer base {
  * {
    @apply border-neutral-200;
  }

  body {
    @apply bg-gradient-to-br from-neutral-50 via-white to-primary-50/30 text-neutral-900 antialiased;
    background-attachment: fixed;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-bold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-6xl lg:text-7xl;
  }

  h2 {
    @apply text-3xl md:text-5xl lg:text-6xl;
  }

  h3 {
    @apply text-2xl md:text-4xl lg:text-5xl;
  }

  h4 {
    @apply text-xl md:text-3xl lg:text-4xl;
  }

  h5 {
    @apply text-lg md:text-2xl lg:text-3xl;
  }

  h6 {
    @apply text-base md:text-xl lg:text-2xl;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-400;
  }
}

@layer components {
  /* 现代化按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-semibold rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 focus:ring-primary-500 shadow-lg hover:shadow-xl hover:shadow-primary-500/25 transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 focus:ring-secondary-500 shadow-lg hover:shadow-xl hover:shadow-secondary-500/25 transform hover:-translate-y-0.5;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:from-accent-600 hover:to-accent-700 focus:ring-accent-500 shadow-lg hover:shadow-xl hover:shadow-accent-500/25 transform hover:-translate-y-0.5;
  }

  .btn-outline {
    @apply bg-white/80 backdrop-blur-sm text-neutral-700 border-2 border-neutral-200 hover:bg-white hover:border-primary-300 hover:text-primary-700 focus:ring-primary-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
  }

  .btn-ghost {
    @apply bg-transparent text-neutral-600 hover:bg-neutral-100 hover:text-neutral-900 focus:ring-neutral-500;
  }

  /* 现代化卡片样式 */
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-primary-500/10 hover:-translate-y-1;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-white via-white to-primary-50/50 rounded-2xl shadow-lg border border-primary-100/50 overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-primary-500/20 hover:-translate-y-1;
  }

  .card-glass {
    @apply bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-lg transition-all duration-300 hover:bg-white/20 hover:shadow-xl;
  }

  /* 现代化表单样式 */
  .form-input {
    @apply mt-1 block w-full rounded-xl border-neutral-200 bg-white/80 backdrop-blur-sm shadow-sm transition-all duration-200 focus:border-primary-500 focus:ring-primary-500 focus:bg-white placeholder:text-neutral-400;
  }

  .form-label {
    @apply block text-sm font-semibold text-neutral-700 mb-2;
  }

  .form-error {
    @apply mt-2 text-sm text-red-600 font-medium;
  }

  /* 渐变文字 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 via-secondary-500 to-accent-500 bg-clip-text text-transparent;
  }

  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-700 bg-clip-text text-transparent;
  }

  /* 玻璃态效果 */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-strong {
    @apply bg-white/20 backdrop-blur-lg border border-white/30;
  }

  /* 发光效果 */
  .glow {
    @apply shadow-glow;
  }

  .glow-lg {
    @apply shadow-glow-lg;
  }

  /* 网格背景 */
  .bg-grid {
    background-image:
      linear-gradient(rgba(168, 85, 247, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(168, 85, 247, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* 动画类 */
  .animate-on-scroll {
    @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
  }

  .animate-on-scroll.in-view {
    @apply opacity-100 translate-y-0;
  }

  /* 响应式容器 */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 分隔线 */
  .divider {
    @apply h-px bg-gradient-to-r from-transparent via-neutral-200 to-transparent;
  }

  .divider-gradient {
    @apply h-px bg-gradient-to-r from-transparent via-primary-300 to-transparent;
  }
}