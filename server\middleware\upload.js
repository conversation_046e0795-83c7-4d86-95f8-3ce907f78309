const path = require('path');
const multer = require('multer');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
console.log('上传目录路径:', uploadDir);
if (!fs.existsSync(uploadDir)) {
  console.log('创建上传目录...');
  fs.mkdirSync(uploadDir, { recursive: true });
} else {
  console.log('上传目录已存在');
}

// 配置存储
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    console.log('文件保存目录:', uploadDir);
    cb(null, uploadDir);
  },
  filename: function(req, file, cb) {
    // 创建唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const filename = `${file.fieldname}-${uniqueSuffix}${ext}`;
    console.log('生成文件名:', filename);
    cb(null, filename);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedFileTypes = /jpeg|jpg|png|gif|webp/;
  // 检查扩展名
  const extname = allowedFileTypes.test(path.extname(file.originalname).toLowerCase());
  // 检查MIME类型
  const mimetype = allowedFileTypes.test(file.mimetype);

  if (extname && mimetype) {
    return cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件！'), false);
  }
};

// 初始化multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: process.env.MAX_FILE_UPLOAD || 5000000, // 默认5MB
    files: 30, // 最多30个文件
    fields: 30  // 最多30个字段
  },
  fileFilter: fileFilter
});

module.exports = upload;