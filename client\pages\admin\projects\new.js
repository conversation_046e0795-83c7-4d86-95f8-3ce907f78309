import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function NewProject({ user }) {
  const router = useRouter();

  // 如果用户未登录，重定向到登录页面
  // 否则重定向到编辑页面，使用 'new' 作为 ID
  useEffect(() => {
    if (!user) {
      router.push('/login');
    } else {
      router.push('/admin/projects/edit/new');
    }
  }, [user, router]);

  return (
    <div>
      <Head>
        <title>添加新项目 | 项目作品集管理系统</title>
      </Head>
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    </div>
  );
}