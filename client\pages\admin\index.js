import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import axios from 'axios';
import AdminLayout from '../../components/layouts/AdminLayout';

export default function AdminDashboard({ user }) {
  const router = useRouter();
  const [stats, setStats] = useState({
    projects: 0,
    categories: 0,
    uploads: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 如果用户未登录，重定向到登录页面
  useEffect(() => {
    if (!user) {
      router.push('/login');
    } else {
      fetchStats();
    }
  }, [user, router]);

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      // 获取项目数量
      const projectsRes = await axios.get('/api/projects');
      // 获取分类数量
      const categoriesRes = await axios.get('/api/categories');
      
      setStats({
        projects: projectsRes.data.count || 0,
        categories: categoriesRes.data.count || 0,
        uploads: 0 // 暂时没有直接获取上传文件数量的API
      });
    } catch (err) {
      console.error('获取统计数据失败:', err);
      setError('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return null; // 等待重定向
  }

  return (
    <AdminLayout user={user} title="管理面板">
      <Head>
        <title>管理面板 | 项目作品集管理系统</title>
      </Head>

      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <h1 className="text-2xl font-semibold text-gray-900">管理面板</h1>
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          {/* 欢迎消息 */}
          <div className="bg-white shadow rounded-lg p-6 mt-6">
            <h2 className="text-lg font-medium text-gray-900">欢迎回来，{user.username}！</h2>
            <p className="mt-1 text-sm text-gray-500">
              这里是您的学习项目管理系统。您可以管理学习项目、分类和上传文件。
            </p>
          </div>

          {/* 统计卡片 */}
          <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            {/* 项目统计 */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary-100 rounded-md p-3">
                    <svg className="h-6 w-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">项目总数</dt>
                      <dd>
                        {loading ? (
                          <div className="animate-pulse h-8 w-16 bg-gray-200 rounded"></div>
                        ) : (
                          <div className="text-lg font-medium text-gray-900">{stats.projects}</div>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <Link href="/admin/projects" className="font-medium text-primary-600 hover:text-primary-500">
                    查看所有项目
                  </Link>
                </div>
              </div>
            </div>

            {/* 分类统计 */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary-100 rounded-md p-3">
                    <svg className="h-6 w-6 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">分类总数</dt>
                      <dd>
                        {loading ? (
                          <div className="animate-pulse h-8 w-16 bg-gray-200 rounded"></div>
                        ) : (
                          <div className="text-lg font-medium text-gray-900">{stats.categories}</div>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <Link href="/admin/categories" className="font-medium text-primary-600 hover:text-primary-500">
                    管理分类
                  </Link>
                </div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <h3 className="text-lg font-medium text-gray-900">快速操作</h3>
                <div className="mt-4 space-y-2">
                  <Link href="/admin/projects/new" className="block text-sm font-medium text-primary-600 hover:text-primary-500">
                    创建新项目
                  </Link>
                  <Link href="/admin/categories/new" className="block text-sm font-medium text-primary-600 hover:text-primary-500">
                    添加新分类
                  </Link>
                  <Link href="/admin/uploads" className="block text-sm font-medium text-primary-600 hover:text-primary-500">
                    管理上传文件
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* 最近活动 */}
          <div className="mt-8 bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">系统信息</h2>
            <div className="border-t border-gray-200 pt-4">
              <dl className="divide-y divide-gray-200">
                <div className="py-3 flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">系统版本</dt>
                  <dd className="text-sm text-gray-900">1.0.0</dd>
                </div>
                <div className="py-3 flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">用户角色</dt>
                  <dd className="text-sm text-gray-900">{user.role}</dd>
                </div>
                <div className="py-3 flex justify-between">
                  <dt className="text-sm font-medium text-gray-500">API状态</dt>
                  <dd className="text-sm text-green-600">正常</dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}