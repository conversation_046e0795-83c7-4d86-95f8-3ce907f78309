import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import axios from 'axios';
import ProjectCard from '../components/projects/ProjectCard';
import CategoryFilter from '../components/CategoryFilter';
import AnimateOnScroll from '../components/AnimateOnScroll';
import Hero from '../components/Hero';

export default function Home() {
  const [featuredProjects, setFeaturedProjects] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesRes = await axios.get('/api/categories');
        setCategories(categoriesRes.data.data);
      } catch (err) {
        console.error('获取分类错误:', err);
        setError('获取分类失败，请稍后再试');
      }
    };

    fetchCategories();
  }, []);

  // 根据选中的分类获取项目
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        let url;

        if (selectedCategory === 'all') {
          // 获取所有特色项目
          url = '/api/projects/featured?limit=6';
        } else {
          // 获取指定分类的特色项目
          url = `/api/projects/category/${selectedCategory}?featured=true&limit=6`;
        }

        const projectsRes = await axios.get(url);
        setFeaturedProjects(projectsRes.data.data || []);
        setError(null);
      } catch (err) {
        console.error('获取项目错误:', err);
        setError('获取项目失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [selectedCategory]);

  // 处理分类变化
  const handleCategoryChange = (categoryId) => {
    setSelectedCategory(categoryId);
  };

  return (
    <div>
      <Head>
        <title>小小怪下士的个人主页 | 展示我的项目</title>
        <meta name="description" content="浏览小小怪下士的个人主页，查看我的学习项目和编程心得" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main>
        {/* 英雄区域 */}
        <Hero />

        {/* 特色项目区域 */}
        <section className="py-20 lg:py-32 relative overflow-hidden">
          {/* 背景装饰 */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-50/50 via-white to-secondary-50/50" />
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-400/20 to-secondary-400/20 rounded-full blur-3xl" />
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-accent-400/20 to-primary-400/20 rounded-full blur-3xl" />

          <div className="relative container-custom">
            <AnimateOnScroll>
              <div className="text-center mb-16">
                <span className="inline-block px-4 py-2 bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-700 rounded-full text-sm font-semibold mb-4">
                  ✨ 学习成果
                </span>
                <h2 className="text-gradient mb-6">学习项目</h2>
                <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
                  这里展示了我在学习过程中完成的一些项目，每个项目都记录了我在不同技术领域的
                  <span className="text-gradient-primary font-semibold"> 学习和成长 </span>
                </p>
              </div>
            </AnimateOnScroll>

            {/* 分类筛选器 */}
            <AnimateOnScroll delay={100}>
              <div className="mb-12">
                <CategoryFilter
                  categories={categories}
                  onFilterChange={handleCategoryChange}
                  activeCategory={selectedCategory}
                />
              </div>
            </AnimateOnScroll>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-secondary-600 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                </div>
              </div>
            ) : error ? (
              <div className="text-center">
                <div className="inline-flex items-center px-6 py-4 bg-red-50 border border-red-200 rounded-xl text-red-600">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {error}
                </div>
              </div>
            ) : featuredProjects.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
                {featuredProjects.map((project, index) => (
                  <AnimateOnScroll key={project._id} delay={index * 200}>
                    <ProjectCard project={project} />
                  </AnimateOnScroll>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-gradient-to-r from-primary-100 to-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-12 h-12 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <p className="text-neutral-500 text-lg">暂无学习项目</p>
              </div>
            )}

            <AnimateOnScroll delay={300}>
              <div className="text-center mt-16">
                <Link href="/projects" className="btn btn-primary group">
                  查看所有项目
                  <svg className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </AnimateOnScroll>
          </div>
        </section>




      </main>
    </div>
  );
}