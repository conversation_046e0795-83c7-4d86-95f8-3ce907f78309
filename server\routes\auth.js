const express = require('express');
const {
  register,
  login,
  getMe,
  logout,
  updateDetails,
  updatePassword
} = require('../controllers/auth');

const router = express.Router();

const { protect, authorize } = require('../middleware/auth');

// 公共路由
router.post('/login', login);
router.get('/logout', logout);

// 受保护的路由
router.get('/me', protect, getMe);
router.put('/updatedetails', protect, updateDetails);
router.put('/updatepassword', protect, updatePassword);

// 管理员路由
router.post('/register', protect, authorize('admin'), register);

module.exports = router;