const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const dotenv = require('dotenv');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');

// 加载环境变量
dotenv.config({ path: './config/config.env' });

// 初始化Express应用
const app = express();

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 路由导入
const projectRoutes = require('./routes/projects');
const authRoutes = require('./routes/auth');
const categoryRoutes = require('./routes/categories');
const uploadRoutes = require('./routes/upload');

// 路由中间件
app.use('/api/projects', projectRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/upload', uploadRoutes);

// 基础路由
app.get('/', (req, res) => {
  res.send('API 运行中...');
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: err.message || '服务器错误'
  });
});

// 连接数据库
const connectDB = async () => {
  try {
    // 使用环境变量中的MongoDB连接字符串，如果不存在则使用本地MongoDB
    const conn = await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/portfolio');
    console.log(`MongoDB 已连接: ${conn.connection.host}`);
  } catch (error) {
    console.error(`错误: ${error.message}`);
    process.exit(1);
  }
};

// 启动服务器
const PORT = process.env.PORT || 5000;
connectDB().then(() => {
  app.listen(PORT, () => {
    console.log(`服务器运行在端口: ${PORT}`);
  });
});