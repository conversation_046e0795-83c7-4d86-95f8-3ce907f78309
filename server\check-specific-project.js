const mongoose = require('mongoose');
const Project = require('./models/Project');

mongoose.connect('mongodb://localhost:27017/xiaoxiaoguai_homepage')
  .then(async () => {
    console.log('数据库连接成功');
    
    const projectId = '68878e30b6e673b609bd9c';
    console.log('查找项目ID:', projectId);
    
    try {
      const project = await Project.findById(projectId);
      if (project) {
        console.log('找到项目:', project.title);
        console.log('封面图片:', project.coverImage);
        console.log('图片列表:', project.images);
      } else {
        console.log('项目不存在');
      }
    } catch (error) {
      console.log('查找项目时出错:', error.message);
    }
    
    // 列出所有现有项目
    console.log('\n所有现有项目:');
    const allProjects = await Project.find({}).select('_id title');
    allProjects.forEach(project => {
      console.log(`ID: ${project._id}, 标题: ${project.title}`);
    });
    
    process.exit(0);
  })
  .catch(err => {
    console.error('数据库连接失败:', err);
    process.exit(1);
  });
