const express = require('express');
const {
  getFiles,
  uploadFile,
  uploadMultipleFiles,
  deleteFile,
  setProjectCover,
  addProjectImages,
  removeProjectImage
} = require('../controllers/upload');

const router = express.Router();

const { protect, authorize } = require('../middleware/auth');
const upload = require('../middleware/upload');

// Multer错误处理中间件
const handleMulterError = (err, req, res, next) => {
  if (err) {
    console.error('Multer错误:', err);

    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: '文件大小超过限制（最大5MB）'
      });
    }

    if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: '文件数量超过限制'
      });
    }

    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        error: '意外的文件字段名'
      });
    }

    if (err.message === '只允许上传图片文件！') {
      return res.status(400).json({
        success: false,
        error: '只允许上传图片文件（JPG、PNG、GIF、WEBP）'
      });
    }

    return res.status(400).json({
      success: false,
      error: err.message || '文件上传失败'
    });
  }
  next();
};

// 获取文件列表
router.get('/', protect, authorize('admin', 'editor'), getFiles);

// 单文件上传
router.post('/', protect, authorize('admin', 'editor'), upload.single('file'), handleMulterError, uploadFile);

// 多文件上传
router.post('/multiple', protect, authorize('admin', 'editor'), upload.array('files', 30), handleMulterError, uploadMultipleFiles);

// 删除文件
router.delete('/:filename', protect, authorize('admin', 'editor'), deleteFile);

// 设置项目封面图片
router.put('/cover/:id', protect, authorize('admin', 'editor'), upload.single('file'), handleMulterError, setProjectCover);

// 添加项目图片
router.put('/images/:id', protect, authorize('admin', 'editor'), upload.array('files', 30), handleMulterError, addProjectImages);

// 删除项目图片
router.delete('/images/:id/:filename', protect, authorize('admin', 'editor'), removeProjectImage);

module.exports = router;