// 错误响应处理中间件
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误信息
  console.error(err);

  // Mongoose错误处理
  // 错误的ObjectId
  if (err.name === 'CastError') {
    const message = `资源未找到`;
    error = { message, status: 404 };
  }

  // Mongoose重复键
  if (err.code === 11000) {
    const message = '已存在相同的记录';
    error = { message, status: 400 };
  }

  // Mongoose验证错误
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = { message, status: 400 };
  }

  res.status(error.status || 500).json({
    success: false,
    error: error.message || '服务器错误'
  });
};

module.exports = errorHandler;