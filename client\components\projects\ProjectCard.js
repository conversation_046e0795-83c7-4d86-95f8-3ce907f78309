import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

export default function ProjectCard({ project }) {
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <div className="group relative card-gradient hover:shadow-2xl hover:shadow-primary-500/20 transition-all duration-500 hover:-translate-y-2">
      {/* 渐变边框效果 */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />

      <div className="relative bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden">
        {/* 图片区域 */}
        <div className="relative h-48 w-full overflow-hidden">
          {/* 获取要显示的图片 - 优先显示封面图，否则显示第一张图片 */}
          {(() => {
            const displayImage = project.coverImage || (project.images && project.images.length > 0 ? project.images[0] : null);

            if (displayImage) {
              return (
                <>
                  <div className={`absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 transition-opacity duration-500 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`}>
                    <div className="flex items-center justify-center h-full">
                      <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
                    </div>
                  </div>

                  <Image
                    src={displayImage.startsWith('http') ? displayImage : `/uploads/${displayImage}`}
                    alt={project.title}
                    layout="fill"
                    objectFit="cover"
                    className={`transition-all duration-700 group-hover:scale-110 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
                    onLoad={() => setImageLoaded(true)}
                  />
                </>
              );
            } else {
              // 没有图片时显示友好的占位符
              return (
                <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <svg className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
                    </svg>
                    <p className="text-sm">暂无图片</p>
                  </div>
                </div>
              );
            }
          })()}

          {/* 悬浮遮罩 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* 精选标签 */}
          {project.featured && (
            <div className="absolute top-3 right-3 bg-gradient-to-r from-accent-500 to-accent-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-bounce-gentle">
              ✨ 精选
            </div>
          )}

          {/* 悬浮时显示的快速操作 */}
          <div className="absolute top-3 left-3 flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            {project.liveUrl && (
              <a
                href={project.liveUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-white/90 backdrop-blur-sm text-primary-600 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 shadow-lg"
                onClick={(e) => e.stopPropagation()}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            )}
            {project.githubUrl && (
              <a
                href={project.githubUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-white/90 backdrop-blur-sm text-neutral-700 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 shadow-lg"
                onClick={(e) => e.stopPropagation()}
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </a>
            )}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          {/* 标题和分类 */}
          <div className="flex justify-between items-start mb-3">
            <Link href={`/projects/${project.slug}`}>
              <h3 className="text-lg font-bold text-neutral-900 group-hover:text-gradient-primary transition-all duration-300 line-clamp-2 cursor-pointer">
                {project.title}
              </h3>
            </Link>
            <span className="ml-2 text-xs font-semibold text-primary-600 bg-primary-50 border border-primary-200 rounded-full px-3 py-1 whitespace-nowrap">
              {project.category?.name || '未分类'}
            </span>
          </div>

          {/* 描述 */}
          <p className="text-sm text-neutral-600 mb-4 line-clamp-2 leading-relaxed">
            {project.shortDescription || project.description.substring(0, 120) + '...'}
          </p>

          {/* 技术标签 */}
          <div className="flex flex-wrap gap-2 mb-5">
            {project.technologies && project.technologies.slice(0, 4).map((tech, index) => (
              <span
                key={index}
                className="text-xs font-medium bg-gradient-to-r from-neutral-100 to-neutral-200 text-neutral-700 rounded-lg px-3 py-1 border border-neutral-200 hover:border-primary-300 hover:from-primary-50 hover:to-primary-100 hover:text-primary-700 transition-all duration-300"
              >
                {tech}
              </span>
            ))}
            {project.technologies && project.technologies.length > 4 && (
              <span className="text-xs font-medium bg-gradient-to-r from-secondary-100 to-accent-100 text-secondary-700 rounded-lg px-3 py-1 border border-secondary-200">
                +{project.technologies.length - 4}
              </span>
            )}
          </div>

          {/* 底部操作区 */}
          <div className="flex justify-between items-center">
            <Link
              href={`/projects/${project.slug}`}
              className="group/link flex items-center space-x-2 text-sm font-semibold text-primary-600 hover:text-primary-700 transition-all duration-300"
            >
              查看详情
              <svg className="w-4 h-4 transition-transform duration-300 group-hover/link:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>

            {/* 项目状态指示器 */}
            <div className="flex items-center space-x-2">
              {project.status === 'completed' && (
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" title="已完成" />
              )}
              {project.status === 'in-progress' && (
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" title="进行中" />
              )}
              {project.status === 'planned' && (
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" title="计划中" />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}