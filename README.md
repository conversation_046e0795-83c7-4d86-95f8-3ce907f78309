# 小小怪下士的个人主页

这是小小怪下士的个人主页，用于分享个人学习成果和编程学习心得的网站，包含前端展示和后台管理系统。

## 技术栈

### 前端
- Next.js - React框架
- Tailwind CSS - 样式框架
- Axios - HTTP客户端

### 后端
- Express - Node.js Web框架
- MongoDB - 数据库
- Mongoose - MongoDB对象模型工具
- JWT - 用户认证

## 功能特点

### 前端展示
- 响应式设计，适配各种设备
- 学习项目展示页面，包含项目详情
- 按类别筛选项目
- 学习成果展示

### 后台管理
- 用户认证和授权
- 项目管理（添加、编辑、删除）
- 类别管理
- 文件上传管理

## 安装和运行

### 前提条件
- Node.js (v14+)
- MongoDB

### 安装步骤

1. 克隆仓库
```bash
git clone <仓库地址>
cd 项目文件夹
```

2. 安装后端依赖
```bash
cd server
npm install
```

3. 配置环境变量
在 `server/config` 目录下创建 `config.env` 文件，并设置以下变量：
```
NODE_ENV=development
PORT=5000
MONGO_URI=<你的MongoDB连接字符串>
JWT_SECRET=<你的JWT密钥>
JWT_EXPIRE=30d
JWT_COOKIE_EXPIRE=30
```

4. 安装前端依赖
```bash
cd ../client
npm install
```

5. 运行应用

后端：
```bash
cd ../server
npm run dev
```

前端：
```bash
cd ../client
npm run dev
```

6. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:5000
- 管理后台: http://localhost:3000/admin

## 项目结构

```
├── client/                 # 前端代码
│   ├── components/         # React组件
│   ├── pages/              # Next.js页面
│   ├── public/             # 静态资源
│   └── styles/             # 样式文件
│
├── server/                 # 后端代码
│   ├── config/             # 配置文件
│   ├── controllers/        # 控制器
│   ├── middleware/         # 中间件
│   ├── models/             # 数据模型
│   ├── routes/             # 路由
│   └── server.js           # 入口文件
│
└── uploads/                # 上传文件存储
```

## 部署

### 前端部署
```bash
cd client
npm run build
npm start
```

### 后端部署
```bash
cd server
NODE_ENV=production npm start
```

## 许可证

MIT