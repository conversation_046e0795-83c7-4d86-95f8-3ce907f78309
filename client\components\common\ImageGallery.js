import { useState } from 'react';
import Image from 'next/image';

export default function ImageGallery({ images }) {
  const [currentIndex, setCurrentIndex] = useState(0);

  // 如果没有图片，显示默认图片
  if (!images || images.length === 0) {
    return (
      <div className="relative w-full h-64 md:h-96 bg-gray-100 rounded-lg overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center text-gray-400">
          <svg className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
      </div>
    );
  }

  // 处理图片路径
  const getImagePath = (imagePath) => {
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    return `/uploads/${imagePath}`;
  };

  // 切换到下一张图片
  const nextImage = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  // 切换到上一张图片
  const prevImage = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + images.length) % images.length);
  };

  // 切换到指定图片
  const goToImage = (index) => {
    setCurrentIndex(index);
  };

  return (
    <div className="space-y-4">
      {/* 主图片 */}
      <div className="relative w-full h-64 md:h-96 bg-gray-100 rounded-lg overflow-hidden">
        <Image
          src={getImagePath(images[currentIndex])}
          alt={`项目图片 ${currentIndex + 1}`}
          layout="fill"
          objectFit="contain"
          className="transition-opacity duration-300"
        />
        
        {/* 导航按钮 */}
        {images.length > 1 && (
          <>
            <button
              onClick={prevImage}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-75 rounded-full p-2 text-gray-800 hover:bg-opacity-100 focus:outline-none transition-all duration-300"
            >
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextImage}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-75 rounded-full p-2 text-gray-800 hover:bg-opacity-100 focus:outline-none transition-all duration-300"
            >
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </>
        )}
      </div>

      {/* 缩略图 */}
      {images.length > 1 && (
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => goToImage(index)}
              className={`relative w-16 h-16 flex-shrink-0 rounded-md overflow-hidden border-2 ${index === currentIndex ? 'border-primary-500' : 'border-transparent'} focus:outline-none transition-all duration-300`}
            >
              <Image
                src={getImagePath(image)}
                alt={`缩略图 ${index + 1}`}
                layout="fill"
                objectFit="cover"
                className={index === currentIndex ? 'opacity-100' : 'opacity-70 hover:opacity-100'}
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
}