import { useState, useEffect } from 'react';
import Head from 'next/head';
import Image from 'next/image';
import AnimateOnScroll from '../components/AnimateOnScroll';

export default function About() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const skills = [
    { name: 'Vue.js', level: 85, color: 'from-green-500 to-green-600' },
    { name: 'Spring Boot', level: 80, color: 'from-green-600 to-green-700' },
    { name: 'React/Next.js', level: 75, color: 'from-blue-500 to-blue-600' },
    { name: 'Java', level: 75, color: 'from-orange-500 to-orange-600' },
    { name: 'Node.js/Express', level: 70, color: 'from-green-700 to-green-800' },
    { name: 'MySQL', level: 70, color: 'from-blue-600 to-blue-700' },
    { name: 'MongoDB', level: 65, color: 'from-green-800 to-green-900' },
    { name: 'Tailwind CSS', level: 80, color: 'from-cyan-500 to-cyan-600' }
  ];

  const experiences = [
    {
      title: '小小怪下士的个人主页',
      company: '个人项目',
      period: '2025.07',
      description: '基于Next.js + Express的全栈个人主页项目，采用前后端分离架构。前端使用Next.js、React、Tailwind CSS构建响应式界面，后端使用Express、MongoDB、JWT实现用户认证和项目管理。包含项目展示、后台管理、文件上传等完整功能。'
    },
    {
      title: 'WARGAME 商品防伪溯源系统',
      company: '毕业设计项目',
      period: '2025.07',
      description: '基于RuoYi框架开发的商品防伪溯源管理系统，采用Spring Boot + Vue3前后端分离架构。实现了防伪码生成管理、二维码展示、移动端扫码验证、扫码记录追踪等核心功能，技术栈包括Vue3、Element Plus、Spring Boot、MySQL、Redis等。'
    },
    {
      title: '都市驿家民宿管理系统',
      company: '毕业设计项目',
      period: '2025.07',
      description: '基于Spring Boot + Vue3的现代化民宿管理解决方案，采用前后端分离架构设计。支持多端访问，包括Web管理端、移动端应用，提供完整的民宿运营管理功能，使用了Vue3、Element Plus、Vite、Spring Boot、MySQL、Redis等技术栈。'
    },
    {
      title: '大数据技术专业',
      company: '山西青年职业学院',
      period: '2022 - 至今',
      description: '主修大数据技术专业，系统学习了数据结构、算法、数据库、大数据处理等专业课程。在学习大数据相关技术的同时，对Web开发产生浓厚兴趣，自主学习并掌握了Java、Python、JavaScript等编程语言，以及Spring Boot、Vue.js等主流开发框架。'
    }
  ];

  return (
    <div>
      <Head>
        <title>关于我 | 小小怪下士的个人主页</title>
        <meta name="description" content="了解更多关于我的学习背景和编程学习经历" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen">
        {/* 英雄区域 */}
        <section className="py-20 lg:py-32 relative overflow-hidden">
          {/* 背景装饰 */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-50/50 via-white to-secondary-50/50" />
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-400/20 to-secondary-400/20 rounded-full blur-3xl" />
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-accent-400/20 to-primary-400/20 rounded-full blur-3xl" />

          <div className="relative container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* 左侧内容 */}
              <AnimateOnScroll>
                <div>
                  <span className="inline-block px-4 py-2 bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                    👋 关于我
                  </span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 mb-6">
                    全栈开发者 &
                    <span className="text-gradient-primary"> 技术实践者</span>
                  </h1>
                  <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
                    我是一名大数据技术专业的学生，在学习大数据相关技术的过程中对Web开发产生了浓厚兴趣。
                    现专注于全栈开发，熟练掌握Spring Boot + Vue.js和Next.js + Express技术栈，已独立完成多个企业级项目。
                  </p>
                  <p className="text-lg text-neutral-600 mb-8 leading-relaxed">
                    我热衷于将理论知识转化为实际项目，通过不断的技术实践和项目开发来提升自己的编程能力。
                    这个个人主页就是我使用Next.js + Express技术栈开发的作品，用来展示我的项目经验和技术成果。
                  </p>
                  
                  {/* 统计数据 */}
                  <div className="grid grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-gradient-primary mb-2">3</div>
                      <div className="text-sm text-neutral-600">完成项目</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-gradient-secondary mb-2">3+</div>
                      <div className="text-sm text-neutral-600">学习年限</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-gradient-accent mb-2">12+</div>
                      <div className="text-sm text-neutral-600">掌握技术</div>
                    </div>
                  </div>
                </div>
              </AnimateOnScroll>

              {/* 右侧图片 */}
              <AnimateOnScroll delay={200}>
                <div className="relative">
                  <div className="relative w-full h-96 bg-gradient-to-br from-neutral-100 to-neutral-200 rounded-2xl overflow-hidden">
                    <Image
                      src="/uploads/user.png"
                      alt="个人照片"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      style={{ objectFit: 'cover' }}
                      className="rounded-2xl"
                    />
                    {/* 装饰性元素 */}
                    <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full opacity-20 blur-xl" />
                    <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-accent-500 to-primary-500 rounded-full opacity-20 blur-xl" />
                  </div>
                </div>
              </AnimateOnScroll>
            </div>
          </div>
        </section>

        {/* 技能区域 */}
        <section className="py-20 bg-white">
          <div className="container-custom">
            <AnimateOnScroll>
              <div className="text-center mb-16">
                <span className="inline-block px-4 py-2 bg-gradient-to-r from-secondary-100 to-accent-100 text-secondary-700 rounded-full text-sm font-semibold mb-4">
                  � 技术技能
                </span>
                <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">掌握的技术栈</h2>
                <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
                  这些是我在项目开发中熟练使用的技术，通过实际项目不断精进
                </p>
              </div>
            </AnimateOnScroll>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {skills.map((skill, index) => (
                <AnimateOnScroll key={skill.name} delay={index * 100}>
                  <div className="bg-white p-6 rounded-xl border border-neutral-200 hover:shadow-lg transition-shadow duration-300">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="font-semibold text-neutral-900">{skill.name}</h3>
                      <span className="text-sm text-neutral-600">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-neutral-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full bg-gradient-to-r ${skill.color} transition-all duration-1000 ease-out`}
                        style={{ 
                          width: isVisible ? `${skill.level}%` : '0%',
                          transitionDelay: `${index * 100}ms`
                        }}
                      />
                    </div>
                  </div>
                </AnimateOnScroll>
              ))}
            </div>
          </div>
        </section>

        {/* 工作经验区域 */}
        <section className="py-20 bg-gradient-to-br from-neutral-50 to-primary-50/30">
          <div className="container-custom">
            <AnimateOnScroll>
              <div className="text-center mb-16">
                <span className="inline-block px-4 py-2 bg-gradient-to-r from-accent-100 to-primary-100 text-accent-700 rounded-full text-sm font-semibold mb-4">
                  🚀 项目经历
                </span>
                <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">项目与学习历程</h2>
                <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
                  我的项目开发经历和专业学习历程
                </p>
              </div>
            </AnimateOnScroll>

            <div className="max-w-4xl mx-auto">
              {experiences.map((exp, index) => (
                <AnimateOnScroll key={index} delay={index * 200}>
                  <div className="relative pl-8 pb-12 last:pb-0">
                    {/* 时间线 */}
                    <div className="absolute left-0 top-0 w-4 h-4 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full" />
                    {index < experiences.length - 1 && (
                      <div className="absolute left-2 top-4 w-0.5 h-full bg-gradient-to-b from-primary-200 to-transparent" />
                    )}
                    
                    {/* 内容 */}
                    <div className="bg-white p-6 rounded-xl border border-neutral-200 hover:shadow-lg transition-shadow duration-300">
                      <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-3">
                        <h3 className="text-xl font-bold text-neutral-900">{exp.title}</h3>
                        <span className="text-sm text-primary-600 bg-primary-50 px-3 py-1 rounded-full mt-2 md:mt-0">
                          {exp.period}
                        </span>
                      </div>
                      <h4 className="text-lg font-semibold text-secondary-600 mb-3">{exp.company}</h4>
                      <p className="text-neutral-600 leading-relaxed">{exp.description}</p>
                    </div>
                  </div>
                </AnimateOnScroll>
              ))}
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
