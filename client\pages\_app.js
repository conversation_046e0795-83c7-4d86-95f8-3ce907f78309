import '../styles/globals.css';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '../components/layouts/Layout';
import axios from 'axios';

// 设置axios默认配置
axios.defaults.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
axios.defaults.headers.post['Content-Type'] = 'application/json';

function MyApp({ Component, pageProps }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已登录
    const checkUserLoggedIn = async () => {
      try {
        const token = localStorage.getItem('token');
        if (token) {
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          const { data } = await axios.get('/api/auth/me');
          setUser(data.data);
        }
      } catch (error) {
        console.error('身份验证错误:', error);
        localStorage.removeItem('token');
      } finally {
        setLoading(false);
      }
    };

    checkUserLoggedIn();
  }, []);

  const login = async (email, password) => {
    try {
      const { data } = await axios.post('/api/auth/login', { email, password });
      localStorage.setItem('token', data.token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${data.token}`;
      const userData = await axios.get('/api/auth/me');
      setUser(userData.data.data);
      return { success: true };
    } catch (error) {
      console.error('登录错误:', error);
      return { 
        success: false, 
        message: error.response?.data?.error || '登录失败'
      };
    }
  };

  const logout = async () => {
    try {
      await axios.get('/api/auth/logout');
    } catch (error) {
      console.error('登出错误:', error);
    } finally {
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
      setUser(null);
    }
  };

  // 检查是否是管理面板页面
  const isAdminPage = router.pathname.startsWith('/admin');

  // 如果是管理面板页面，不使用主站布局
  if (isAdminPage) {
    return (
      <Component
        {...pageProps}
        user={user}
        login={login}
        logout={logout}
        loading={loading}
      />
    );
  }

  return (
    <Layout user={user} logout={logout}>
      <Component
        {...pageProps}
        user={user}
        login={login}
        logout={logout}
        loading={loading}
      />
    </Layout>
  );
}

export default MyApp;