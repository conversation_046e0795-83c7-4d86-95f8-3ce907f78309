import { useState, useEffect } from 'react';
import Head from 'next/head';
import axios from 'axios';
import ProjectCard from '../../components/projects/ProjectCard';
import Pagination from '../../components/common/Pagination';
import AnimateOnScroll from '../../components/AnimateOnScroll';

export default function Projects() {
  const [projects, setProjects] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  const limit = 9; // 每页显示的项目数量

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const res = await axios.get('/api/categories');
        setCategories(res.data.data);
      } catch (err) {
        console.error('获取分类错误:', err);
      }
    };

    fetchCategories();
  }, []);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        let url = `/api/projects?page=${currentPage}&limit=${limit}&sort=-createdAt`;
        
        // 添加分类过滤
        if (selectedCategory) {
          url = `/api/projects/category/${selectedCategory}`;
        }

        const res = await axios.get(url);
        setProjects(res.data.data);
        
        // 设置总页数
        if (res.data.pagination) {
          const total = res.data.pagination.next 
            ? res.data.pagination.next.page * limit 
            : (res.data.pagination.prev ? (res.data.pagination.prev.page + 2) * limit : limit);
          
          setTotalPages(Math.ceil(total / limit));
        } else {
          setTotalPages(Math.ceil(res.data.count / limit));
        }
        
        setError(null);
      } catch (err) {
        console.error('获取项目错误:', err);
        setError('获取项目失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [currentPage, selectedCategory]);

  // 处理分类变化
  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    setCurrentPage(1); // 重置为第一页
  };

  // 处理搜索
  const handleSearch = (e) => {
    e.preventDefault();
    // 实现搜索功能
    console.log('搜索:', searchTerm);
    // 这里可以添加搜索逻辑，目前后端API还没有实现搜索功能
  };

  // 过滤项目（前端搜索）
  const filteredProjects = searchTerm
    ? projects.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : projects;

  return (
    <div>
      <Head>
        <title>所有项目 | 小小怪下士的个人主页</title>
        <meta name="description" content="浏览我的所有项目作品" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">所有项目</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              浏览我的所有项目作品，了解我的技能和专业知识。
            </p>
          </div>

          {/* 过滤和搜索 */}
          <div className="flex flex-col md:flex-row justify-between mb-8">
            <div className="mb-4 md:mb-0">
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                按分类筛选
              </label>
              <select
                id="category"
                className="form-input"
                value={selectedCategory}
                onChange={handleCategoryChange}
              >
                <option value="">所有分类</option>
                {categories.map((category) => (
                  <option key={category._id} value={category._id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <form onSubmit={handleSearch}>
                <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                  搜索项目
                </label>
                <div className="flex">
                  <input
                    type="text"
                    id="search"
                    className="form-input rounded-r-none"
                    placeholder="搜索项目..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <button
                    type="submit"
                    className="bg-primary-600 text-white px-4 rounded-r-md hover:bg-primary-700"
                  >
                    搜索
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* 项目列表 */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500">{error}</div>
          ) : filteredProjects.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-xl text-gray-600">没有找到项目</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredProjects.map((project, index) => (
                <AnimateOnScroll key={project._id} delay={index * 100}>
                  <ProjectCard project={project} />
                </AnimateOnScroll>
              ))}
            </div>
          )}

          {/* 分页 */}
          {!loading && !error && totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          )}
        </div>
      </main>
    </div>
  );
}