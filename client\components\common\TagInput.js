import { useState, useRef, useEffect } from 'react';

export default function TagInput({
  label,
  name,
  value = [],
  onChange,
  placeholder = '输入标签并按回车添加',
  required = false,
  error = '',
  disabled = false,
  className = '',
  maxTags = 20,
  ...rest
}) {
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef(null);

  // 处理输入变化
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      addTag(inputValue.trim());
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      // 如果输入框为空且按下退格键，删除最后一个标签
      removeTag(value.length - 1);
    }
  };

  // 添加标签
  const addTag = (tag) => {
    if (tag && !value.includes(tag) && value.length < maxTags) {
      const newTags = [...value, tag];
      onChange({ target: { name, value: newTags } });
      setInputValue('');
    }
  };

  // 删除标签
  const removeTag = (index) => {
    const newTags = [...value];
    newTags.splice(index, 1);
    onChange({ target: { name, value: newTags } });
  };

  // 处理粘贴事件
  const handlePaste = (e) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');
    const tags = pasteData.split(/[,;\n\s]/).filter(tag => tag.trim());
    
    // 添加新标签，避免重复
    const newTags = [...value];
    tags.forEach(tag => {
      if (tag && !newTags.includes(tag) && newTags.length < maxTags) {
        newTags.push(tag);
      }
    });
    
    if (newTags.length !== value.length) {
      onChange({ target: { name, value: newTags } });
    }
  };

  // 点击容器时聚焦输入框
  const focusInput = () => {
    if (!disabled && inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div 
        className={`flex flex-wrap items-center p-2 border rounded-md shadow-sm ${error ? 'border-red-300' : 'border-gray-300'} ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''} focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500`}
        onClick={focusInput}
      >
        {value.map((tag, index) => (
          <div 
            key={index} 
            className="flex items-center bg-primary-100 text-primary-800 rounded-full px-3 py-1 text-sm mr-2 mb-2"
          >
            <span>{tag}</span>
            {!disabled && (
              <button 
                type="button" 
                className="ml-1 text-primary-500 hover:text-primary-700 focus:outline-none"
                onClick={(e) => {
                  e.stopPropagation();
                  removeTag(index);
                }}
              >
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        ))}
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          placeholder={value.length === 0 ? placeholder : ''}
          disabled={disabled || value.length >= maxTags}
          className="flex-grow min-w-[120px] p-1 mb-2 text-sm border-0 focus:ring-0 focus:outline-none bg-transparent"
          {...rest}
        />
      </div>
      {error ? (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      ) : (
        <p className="mt-1 text-xs text-gray-500">
          {`已添加 ${value.length}/${maxTags} 个标签，按回车添加，按退格键删除`}
        </p>
      )}
    </div>
  );
}