const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/xiaoxiaoguai_homepage', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`MongoDB 已连接: ${conn.connection.host}`.cyan.underline.bold);
  } catch (error) {
    console.error(`错误: ${error.message}`.red);
    process.exit(1);
  }
};

module.exports = connectDB;