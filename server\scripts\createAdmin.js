const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../config/config.env') });

// 连接数据库
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/portfolio', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// 创建管理员用户
const createAdmin = async () => {
  try {
    // 检查User模型是否已经定义
    let User;
    try {
      User = mongoose.model('User');
    } catch (error) {
      // 如果模型未定义，则导入并定义
      const UserSchema = new mongoose.Schema({
        username: {
          type: String,
          required: [true, '请提供用户名'],
          unique: true,
          trim: true,
          maxlength: [50, '用户名不能超过50个字符']
        },
        email: {
          type: String,
          required: [true, '请提供电子邮件'],
          unique: true,
          match: [
            /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
            '请提供有效的电子邮件'
          ]
        },
        password: {
          type: String,
          required: [true, '请提供密码'],
          minlength: [6, '密码至少需要6个字符'],
          select: false
        },
        role: {
          type: String,
          enum: ['admin', 'editor'],
          default: 'editor'
        },
        resetPasswordToken: String,
        resetPasswordExpire: Date,
        createdAt: {
          type: Date,
          default: Date.now
        },
        updatedAt: {
          type: Date,
          default: Date.now
        }
      });
      
      // 加密密码
      UserSchema.pre('save', async function(next) {
        if (!this.isModified('password')) {
          next();
          return;
        }
        
        const salt = await bcrypt.genSalt(10);
        this.password = await bcrypt.hash(this.password, salt);
        next();
      });
      
      User = mongoose.model('User', UserSchema);
    }

    // 检查是否已存在管理员用户
    const adminExists = await User.findOne({ email: '<EMAIL>' });
    
    if (adminExists) {
      console.log('管理员用户已存在!');
      process.exit(0);
    }

    // 创建新管理员用户
    const admin = await User.create({
      username: 'Administrator',
      email: '<EMAIL>',
      password: 'ldf123456',
      role: 'admin'
    });
    // 注意：密码会通过模型的pre-save钩子自动进行哈希处理

    console.log('管理员用户创建成功!');
    process.exit(0);
  } catch (error) {
    console.error('创建管理员用户时出错:', error);
    process.exit(1);
  }
};

createAdmin();