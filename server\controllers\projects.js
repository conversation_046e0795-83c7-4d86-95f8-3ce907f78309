const Project = require('../models/Project');
const path = require('path');
const fs = require('fs');

// @desc    获取所有项目
// @route   GET /api/projects
// @access  Public
exports.getProjects = async (req, res, next) => {
  try {
    // 构建查询
    let query;
    const reqQuery = { ...req.query };

    // 排除特定字段
    const removeFields = ['select', 'sort', 'page', 'limit'];
    removeFields.forEach(param => delete reqQuery[param]);

    // 创建查询字符串
    let queryStr = JSON.stringify(reqQuery);

    // 创建操作符 ($gt, $gte, etc)
    queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

    // 查找项目
    query = Project.find(JSON.parse(queryStr)).populate('category');

    // 选择特定字段
    if (req.query.select) {
      const fields = req.query.select.split(',').join(' ');
      query = query.select(fields);
    }

    // 排序
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // 分页
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const total = await Project.countDocuments(JSON.parse(queryStr));

    query = query.skip(startIndex).limit(limit);

    // 执行查询
    const projects = await query;

    // 分页结果
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }

    res.status(200).json({
      success: true,
      count: projects.length,
      pagination,
      data: projects
    });
  } catch (err) {
    next(err);
  }
};

// @desc    获取单个项目
// @route   GET /api/projects/:id
// @access  Public
exports.getProject = async (req, res, next) => {
  try {
    let project;
    
    // 检查是通过ID还是slug查询
    if (req.params.id.match(/^[0-9a-fA-F]{24}$/)) {
      // 通过ID查询
      project = await Project.findById(req.params.id).populate('category');
    } else {
      // 通过slug查询
      project = await Project.findOne({ slug: req.params.id }).populate('category');
    }

    if (!project) {
      return res.status(404).json({
        success: false,
        error: '找不到项目'
      });
    }

    res.status(200).json({
      success: true,
      data: project
    });
  } catch (err) {
    next(err);
  }
};

// @desc    创建新项目
// @route   POST /api/projects
// @access  Private
exports.createProject = async (req, res, next) => {
  try {
    // 添加用户到请求体
    req.body.user = req.user.id;

    // 创建项目
    const project = await Project.create(req.body);

    res.status(201).json({
      success: true,
      data: project
    });
  } catch (err) {
    next(err);
  }
};

// @desc    更新项目
// @route   PUT /api/projects/:id
// @access  Private
exports.updateProject = async (req, res, next) => {
  try {
    let project = await Project.findById(req.params.id);

    if (!project) {
      return res.status(404).json({
        success: false,
        error: '找不到项目'
      });
    }

    // 更新项目
    project = await Project.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: project
    });
  } catch (err) {
    next(err);
  }
};

// @desc    删除项目
// @route   DELETE /api/projects/:id
// @access  Private
exports.deleteProject = async (req, res, next) => {
  try {
    const project = await Project.findById(req.params.id);

    if (!project) {
      return res.status(404).json({
        success: false,
        error: '找不到项目'
      });
    }

    // 删除项目相关的图片
    if (project.coverImage) {
      const imagePath = path.join(__dirname, `../uploads/${project.coverImage}`);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }

    if (project.images && project.images.length > 0) {
      project.images.forEach(image => {
        const imagePath = path.join(__dirname, `../uploads/${image}`);
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }
      });
    }

    await project.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

// @desc    获取特色项目
// @route   GET /api/projects/featured
// @access  Public
exports.getFeaturedProjects = async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 3;
    
    const projects = await Project.find({ featured: true, status: 'published' })
      .sort('-createdAt')
      .limit(limit)
      .populate('category');

    res.status(200).json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (err) {
    next(err);
  }
};

// @desc    获取特定分类的项目
// @route   GET /api/projects/category/:categoryId
// @access  Public
exports.getProjectsByCategory = async (req, res, next) => {
  try {
    // 构建查询条件
    const query = {
      category: req.params.categoryId,
      status: 'published'
    };

    // 如果请求特色项目，添加 featured 条件
    if (req.query.featured === 'true') {
      query.featured = true;
    }

    // 获取限制数量
    const limit = parseInt(req.query.limit, 10) || 0;

    let projectQuery = Project.find(query)
      .sort('-createdAt')
      .populate('category');

    // 如果有限制数量，应用限制
    if (limit > 0) {
      projectQuery = projectQuery.limit(limit);
    }

    const projects = await projectQuery;

    res.status(200).json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (err) {
    next(err);
  }
};