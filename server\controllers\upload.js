const path = require('path');
const fs = require('fs');
const Project = require('../models/Project');

// @desc    获取上传的文件列表
// @route   GET /api/upload
// @access  Private
exports.getFiles = async (req, res, next) => {
  try {
    const uploadDir = path.join(__dirname, '../uploads');

    // 检查上传目录是否存在
    if (!fs.existsSync(uploadDir)) {
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }

    // 读取目录中的所有文件
    const files = fs.readdirSync(uploadDir);

    // 获取文件详细信息
    const fileList = files
      .filter(file => {
        // 过滤掉隐藏文件和目录
        return !file.startsWith('.') && fs.statSync(path.join(uploadDir, file)).isFile();
      })
      .map(file => {
        const filePath = path.join(uploadDir, file);
        const stats = fs.statSync(filePath);

        return {
          name: file,
          size: stats.size,
          lastModified: stats.mtime,
          url: `/uploads/${file}`
        };
      })
      .sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified)); // 按修改时间倒序排列

    res.status(200).json({
      success: true,
      count: fileList.length,
      data: fileList
    });
  } catch (err) {
    console.error('获取文件列表错误:', err);
    next(err);
  }
};

// @desc    上传项目图片
// @route   POST /api/upload
// @access  Private
exports.uploadFile = async (req, res, next) => {
  try {
    console.log('单文件上传请求:', {
      file: req.file ? req.file.filename : 'no file',
      body: req.body
    });

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: '请上传文件'
      });
    }

    console.log('文件上传成功:', req.file.filename);

    res.status(200).json({
      success: true,
      data: {
        fileName: req.file.filename,
        filePath: `/uploads/${req.file.filename}`,
        originalName: req.file.originalname,
        size: req.file.size
      }
    });
  } catch (err) {
    console.error('单文件上传错误:', err);
    next(err);
  }
};

// @desc    上传多个项目图片
// @route   POST /api/upload/multiple
// @access  Private
exports.uploadMultipleFiles = async (req, res, next) => {
  try {
    console.log('多文件上传请求:', {
      filesCount: req.files ? req.files.length : 0,
      body: req.body
    });

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请上传文件'
      });
    }

    const fileData = req.files.map(file => ({
      fileName: file.filename,
      filePath: `/uploads/${file.filename}`,
      originalName: file.originalname,
      size: file.size
    }));

    console.log('多文件上传成功:', fileData.map(f => f.fileName));

    res.status(200).json({
      success: true,
      count: req.files.length,
      data: fileData
    });
  } catch (err) {
    console.error('多文件上传错误:', err);
    next(err);
  }
};

// @desc    删除上传的文件
// @route   DELETE /api/upload/:filename
// @access  Private
exports.deleteFile = async (req, res, next) => {
  try {
    const filePath = path.join(__dirname, `../uploads/${req.params.filename}`);
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: '文件不存在'
      });
    }

    // 删除文件
    fs.unlinkSync(filePath);

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

// @desc    设置项目封面图片
// @route   PUT /api/upload/cover/:id
// @access  Private
exports.setProjectCover = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: '请上传文件'
      });
    }

    const project = await Project.findById(req.params.id);

    if (!project) {
      // 删除上传的文件，因为项目不存在
      const filePath = path.join(__dirname, `../uploads/${req.file.filename}`);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      return res.status(404).json({
        success: false,
        error: '找不到项目'
      });
    }

    // 删除旧的封面图片
    if (project.coverImage) {
      const oldImagePath = path.join(__dirname, `../uploads/${project.coverImage}`);
      if (fs.existsSync(oldImagePath)) {
        fs.unlinkSync(oldImagePath);
      }
    }

    // 更新项目封面图片
    project.coverImage = req.file.filename;
    await project.save();

    res.status(200).json({
      success: true,
      data: {
        fileName: req.file.filename,
        filePath: `/uploads/${req.file.filename}`
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    添加项目图片
// @route   PUT /api/upload/images/:id
// @access  Private
exports.addProjectImages = async (req, res, next) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请上传文件'
      });
    }

    const project = await Project.findById(req.params.id);

    if (!project) {
      // 删除上传的文件，因为项目不存在
      req.files.forEach(file => {
        const filePath = path.join(__dirname, `../uploads/${file.filename}`);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      });

      return res.status(404).json({
        success: false,
        error: '找不到项目'
      });
    }

    // 添加新图片到项目
    const newImages = req.files.map(file => file.filename);
    project.images = [...project.images, ...newImages];
    await project.save();

    const fileData = req.files.map(file => ({
      fileName: file.filename,
      filePath: `/uploads/${file.filename}`
    }));

    res.status(200).json({
      success: true,
      count: req.files.length,
      data: fileData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    删除项目图片
// @route   DELETE /api/upload/images/:id/:filename
// @access  Private
exports.removeProjectImage = async (req, res, next) => {
  try {
    const project = await Project.findById(req.params.id);

    if (!project) {
      return res.status(404).json({
        success: false,
        error: '找不到项目'
      });
    }

    // 检查图片是否存在于项目中
    if (!project.images.includes(req.params.filename)) {
      return res.status(404).json({
        success: false,
        error: '找不到图片'
      });
    }

    // 从项目中移除图片
    project.images = project.images.filter(image => image !== req.params.filename);
    await project.save();

    // 删除文件
    const filePath = path.join(__dirname, `../uploads/${req.params.filename}`);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};