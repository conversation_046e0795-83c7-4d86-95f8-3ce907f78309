import { useState, useEffect } from 'react';

const CategoryFilter = ({ categories, onFilterChange, activeCategory: externalActiveCategory }) => {
  const [activeCategory, setActiveCategory] = useState(externalActiveCategory || 'all');

  // 同步外部传入的 activeCategory
  useEffect(() => {
    if (externalActiveCategory !== undefined) {
      setActiveCategory(externalActiveCategory);
    }
  }, [externalActiveCategory]);

  const handleCategoryClick = (categoryId) => {
    setActiveCategory(categoryId);
    onFilterChange(categoryId);
  };

  return (
    <div className="flex flex-wrap justify-center gap-3 mb-8">
      <button
        className={`group relative px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 ${
          activeCategory === 'all'
            ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg shadow-primary-500/25'
            : 'bg-white/80 backdrop-blur-sm text-neutral-700 border border-neutral-200 hover:border-primary-300 hover:text-primary-700 hover:bg-white'
        }`}
        onClick={() => handleCategoryClick('all')}
      >
        <span className="relative z-10">全部</span>
        {activeCategory === 'all' && (
          <div className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur-sm opacity-50 animate-pulse" />
        )}
      </button>

      {categories.map((category, index) => (
        <button
          key={category._id}
          className={`group relative px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 ${
            activeCategory === category._id
              ? 'bg-gradient-to-r from-secondary-500 to-accent-500 text-white shadow-lg shadow-secondary-500/25'
              : 'bg-white/80 backdrop-blur-sm text-neutral-700 border border-neutral-200 hover:border-secondary-300 hover:text-secondary-700 hover:bg-white'
          }`}
          onClick={() => handleCategoryClick(category._id)}
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          <span className="relative z-10">{category.name}</span>
          {activeCategory === category._id && (
            <div className="absolute inset-0 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-2xl blur-sm opacity-50 animate-pulse" />
          )}
        </button>
      ))}
    </div>
  );
};

export default CategoryFilter;