import { useState, useEffect } from 'react';
import ProjectCard from './ProjectCard';

export default function ProjectList({ projects, loading, error }) {
  const [filteredProjects, setFilteredProjects] = useState([]);
  
  useEffect(() => {
    setFilteredProjects(projects);
  }, [projects]);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-20">
        <div className="text-red-500 text-lg">{error}</div>
      </div>
    );
  }

  if (filteredProjects.length === 0) {
    return (
      <div className="text-center py-20">
        <div className="text-gray-500 text-lg">没有找到项目</div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {filteredProjects.map((project) => (
        <ProjectCard key={project._id} project={project} />
      ))}
    </div>
  );
}