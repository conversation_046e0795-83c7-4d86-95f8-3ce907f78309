import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import axios from 'axios';
import AdminLayout from '../../../../components/layouts/AdminLayout';

export default function EditProject({ user }) {
  const router = useRouter();
  const { id } = router.query;
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    content: '',
    technologies: '',
    githubUrl: '',
    demoUrl: '',
    status: 'draft',
    category: '',
    featured: false
  });
  
  const [categories, setCategories] = useState([]);
  const [images, setImages] = useState([]);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [uploadError, setUploadError] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);

  // 如果用户未登录，重定向到登录页面
  useEffect(() => {
    if (!user) {
      router.push('/login');
    } else if (id && id !== 'new') {
      fetchProject();
      fetchCategories();
    } else {
      setLoading(false);
      fetchCategories();
    }
  }, [user, router, id]);

  // 获取项目详情
  const fetchProject = async () => {
    try {
      setLoading(true);
      const res = await axios.get(`/api/projects/${id}`);
      const project = res.data.data;

      setFormData({
        title: project.title || '',
        slug: project.slug || '',
        description: project.description || '',
        content: project.content || '',
        technologies: project.technologies ? project.technologies.join(', ') : '',
        githubUrl: project.githubUrl || '',
        demoUrl: project.demoUrl || '',
        status: project.status || 'draft',
        category: project.category ? project.category._id : '',
        featured: project.featured || false
      });

      setImages(project.images || []);
    } catch (err) {
      console.error('获取项目详情失败:', err);
      if (err.response?.status === 404) {
        setError('项目不存在，可能已被删除');
        // 3秒后自动跳转到项目列表
        setTimeout(() => {
          router.push('/admin/projects');
        }, 3000);
      } else {
        setError('获取项目详情失败: ' + (err.response?.data?.error || err.message));
      }
    } finally {
      setLoading(false);
    }
  };

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const res = await axios.get('/api/categories');
      setCategories(res.data.data);
    } catch (err) {
      console.error('获取分类失败:', err);
      setError('获取分类列表失败');
    }
  };

  // 处理表单输入变化
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const inputValue = type === 'checkbox' ? checked : value;

    // 如果修改的是标题，并且 slug 字段为空，则自动生成 slug
    if (name === 'title' && !formData.slug) {
      const generatedSlug = value
        .toLowerCase()
        .replace(/[^\w\s-]/g, '') // 移除特殊字符
        .replace(/\s+/g, '-') // 将空格替换为连字符
        .replace(/--+/g, '-') // 将多个连字符替换为单个连字符
        .trim(); // 移除首尾空格

      setFormData({
        ...formData,
        title: value,
        slug: generatedSlug
      });
    } else {
      setFormData({
        ...formData,
        [name]: inputValue
      });
    }
  };

  // 处理图片上传
  const handleImageUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    const formData = new FormData();

    try {
      setUploadProgress(0);
      setUploadError('');

      let res;

      if (files.length === 1) {
        // 单文件上传
        formData.append('file', files[0]);
        res = await axios.post('/api/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percentCompleted);
          }
        });

        // 单文件上传返回的数据结构不同
        setUploadedImages([...uploadedImages, res.data.data.fileName]);
      } else {
        // 多文件上传
        files.forEach(file => {
          formData.append('files', file);
        });
        res = await axios.post('/api/upload/multiple', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percentCompleted);
          }
        });

        // 多文件上传返回的数据结构
        const newImages = res.data.data.map(item => item.fileName);
        setUploadedImages([...uploadedImages, ...newImages]);
      }

      setUploadProgress(0);
    } catch (err) {
      console.error('上传图片失败:', err);
      setUploadError('上传图片失败: ' + (err.response?.data?.error || err.message));
      setUploadProgress(0);
    }
  };

  // 删除已上传的图片
  const handleRemoveUploadedImage = (filename) => {
    setUploadedImages(uploadedImages.filter(img => img !== filename));
  };

  // 删除已有的图片
  const handleRemoveExistingImage = (filename) => {
    setImages(images.filter(img => img !== filename));
  };

  // 提交表单
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setSubmitting(true);

    try {
      const projectData = {
        ...formData,
        technologies: formData.technologies.split(',').map(tech => tech.trim()).filter(tech => tech),
        images: [...images, ...uploadedImages]
      };

      let res;
      if (id && id !== 'new') {
        res = await axios.put(`/api/projects/${id}`, projectData);
      } else {
        res = await axios.post('/api/projects', projectData);
      }

      setSuccess('项目保存成功！');
      if (id === 'new') {
        router.push(`/admin/projects/edit/${res.data.data._id}`);
      }
    } catch (err) {
      console.error('保存项目失败:', err);
      setError('保存项目失败: ' + (err.response?.data?.error || err.message));
    } finally {
      setSubmitting(false);
    }
  };

  if (!user) {
    return null; // 等待重定向
  }

  return (
    <AdminLayout user={user} title={id === 'new' ? '添加项目' : '编辑项目'}>
      <Head>
        <title>{id === 'new' ? '添加项目' : '编辑项目'} | 项目作品集管理系统</title>
      </Head>

      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-semibold text-gray-900">
              {id === 'new' ? '添加新项目' : '编辑项目'}
            </h1>
            <Link href="/admin/projects" className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              返回项目列表
            </Link>
          </div>

          {loading ? (
            <div className="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">加载中...</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="mt-6 space-y-8 divide-y divide-gray-200">
              <div className="space-y-8 divide-y divide-gray-200 sm:space-y-5">
                {error && (
                  <div className="rounded-md bg-red-50 p-4 mb-6">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">{error}</h3>
                      </div>
                    </div>
                  </div>
                )}

                {success && (
                  <div className="rounded-md bg-green-50 p-4 mb-6">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-green-800">{success}</h3>
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                  <div className="md:grid md:grid-cols-3 md:gap-6">
                    <div className="md:col-span-1">
                      <h3 className="text-lg font-medium leading-6 text-gray-900">项目信息</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        填写项目的基本信息，包括标题、描述和内容。
                      </p>
                    </div>
                    <div className="mt-5 md:mt-0 md:col-span-2">
                      <div className="grid grid-cols-6 gap-6">
                        <div className="col-span-6 sm:col-span-4">
                          <label htmlFor="title" className="block text-sm font-medium text-gray-700">项目标题</label>
                          <input
                            type="text"
                            name="title"
                            id="title"
                            required
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={formData.title}
                            onChange={handleChange}
                          />
                        </div>
                        
                        <div className="col-span-6 sm:col-span-4">
                          <label htmlFor="slug" className="block text-sm font-medium text-gray-700">项目路径 (Slug)</label>
                          <input
                            type="text"
                            name="slug"
                            id="slug"
                            required
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={formData.slug || ''}
                            onChange={handleChange}
                            placeholder="my-project-name"
                          />
                          <p className="mt-2 text-sm text-gray-500">用于URL的唯一标识符，只能包含小写字母、数字和连字符</p>
                        </div>

                        <div className="col-span-6">
                          <label htmlFor="description" className="block text-sm font-medium text-gray-700">项目简介</label>
                          <textarea
                            id="description"
                            name="description"
                            rows="3"
                            required
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={formData.description}
                            onChange={handleChange}
                          ></textarea>
                          <p className="mt-2 text-sm text-gray-500">简短描述项目的主要功能和特点。</p>
                        </div>

                        <div className="col-span-6">
                          <label htmlFor="content" className="block text-sm font-medium text-gray-700">项目详情</label>
                          <textarea
                            id="content"
                            name="content"
                            rows="6"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={formData.content}
                            onChange={handleChange}
                          ></textarea>
                          <p className="mt-2 text-sm text-gray-500">详细描述项目的背景、功能、实现方式等。</p>
                        </div>

                        <div className="col-span-6 sm:col-span-3">
                          <label htmlFor="technologies" className="block text-sm font-medium text-gray-700">使用技术</label>
                          <input
                            type="text"
                            name="technologies"
                            id="technologies"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            placeholder="React, Node.js, MongoDB"
                            value={formData.technologies}
                            onChange={handleChange}
                          />
                          <p className="mt-2 text-sm text-gray-500">使用逗号分隔多个技术。</p>
                        </div>

                        <div className="col-span-6 sm:col-span-3">
                          <label htmlFor="category" className="block text-sm font-medium text-gray-700">项目分类</label>
                          <select
                            id="category"
                            name="category"
                            className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                            value={formData.category}
                            onChange={handleChange}
                          >
                            <option value="">选择分类</option>
                            {categories.map((category) => (
                              <option key={category._id} value={category._id}>
                                {category.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div className="col-span-6 sm:col-span-3">
                          <label htmlFor="githubUrl" className="block text-sm font-medium text-gray-700">GitHub 链接</label>
                          <input
                            type="url"
                            name="githubUrl"
                            id="githubUrl"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            placeholder="https://github.com/username/repo"
                            value={formData.githubUrl}
                            onChange={handleChange}
                          />
                        </div>

                        <div className="col-span-6 sm:col-span-3">
                          <label htmlFor="demoUrl" className="block text-sm font-medium text-gray-700">演示链接</label>
                          <input
                            type="url"
                            name="demoUrl"
                            id="demoUrl"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            placeholder="https://example.com"
                            value={formData.demoUrl}
                            onChange={handleChange}
                          />
                        </div>

                        <div className="col-span-6 sm:col-span-3">
                          <label htmlFor="status" className="block text-sm font-medium text-gray-700">项目状态</label>
                          <select
                            id="status"
                            name="status"
                            className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                            value={formData.status}
                            onChange={handleChange}
                          >
                            <option value="draft">草稿</option>
                            <option value="published">已发布</option>
                          </select>
                        </div>

                        <div className="col-span-6 sm:col-span-3">
                          <div className="flex items-center h-full">
                            <div className="flex items-center h-5">
                              <input
                                id="featured"
                                name="featured"
                                type="checkbox"
                                className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                                checked={formData.featured}
                                onChange={handleChange}
                              />
                            </div>
                            <div className="ml-3 text-sm">
                              <label htmlFor="featured" className="font-medium text-gray-700">设为特色项目</label>
                              <p className="text-gray-500">特色项目将在首页展示</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6 mt-6">
                  <div className="md:grid md:grid-cols-3 md:gap-6">
                    <div className="md:col-span-1">
                      <h3 className="text-lg font-medium leading-6 text-gray-900">项目图片</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        上传项目的截图或相关图片。
                      </p>
                    </div>
                    <div className="mt-5 md:mt-0 md:col-span-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">已有图片</label>
                        {images.length === 0 ? (
                          <p className="text-sm text-gray-500 mt-2">暂无图片</p>
                        ) : (
                          <div className="mt-2 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                            {images.map((image, index) => (
                              <div key={index} className="relative">
                                <div className="group block w-full aspect-w-10 aspect-h-7 rounded-lg bg-gray-100 overflow-hidden">
                                  <img src={`/uploads/${image}`} alt="" className="object-cover pointer-events-none" />
                                  <button
                                    type="button"
                                    className="absolute inset-0 focus:outline-none"
                                    onClick={() => handleRemoveExistingImage(image)}
                                  >
                                    <span className="sr-only">删除图片</span>
                                  </button>
                                </div>
                                <button
                                  type="button"
                                  className="absolute top-0 right-0 bg-red-100 rounded-bl-lg p-1 text-red-600 hover:text-red-500 focus:outline-none"
                                  onClick={() => handleRemoveExistingImage(image)}
                                >
                                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                </button>
                              </div>
                            ))}
                          </div>
                        )}

                        <label className="block text-sm font-medium text-gray-700 mt-6">新上传的图片</label>
                        {uploadedImages.length === 0 ? (
                          <p className="text-sm text-gray-500 mt-2">暂无新上传图片</p>
                        ) : (
                          <div className="mt-2 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                            {uploadedImages.map((image, index) => (
                              <div key={index} className="relative">
                                <div className="group block w-full aspect-w-10 aspect-h-7 rounded-lg bg-gray-100 overflow-hidden">
                                  <img src={`/uploads/${image}`} alt="" className="object-cover pointer-events-none" />
                                  <button
                                    type="button"
                                    className="absolute inset-0 focus:outline-none"
                                    onClick={() => handleRemoveUploadedImage(image)}
                                  >
                                    <span className="sr-only">删除图片</span>
                                  </button>
                                </div>
                                <button
                                  type="button"
                                  className="absolute top-0 right-0 bg-red-100 rounded-bl-lg p-1 text-red-600 hover:text-red-500 focus:outline-none"
                                  onClick={() => handleRemoveUploadedImage(image)}
                                >
                                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                </button>
                              </div>
                            ))}
                          </div>
                        )}

                        <div className="mt-6">
                          <label className="block text-sm font-medium text-gray-700">上传新图片</label>
                          <div className="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div className="space-y-1 text-center">
                              <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                              <div className="flex text-sm text-gray-600">
                                <label htmlFor="file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                  <span>上传图片</span>
                                  <input id="file-upload" name="file-upload" type="file" className="sr-only" multiple onChange={handleImageUpload} accept="image/*" />
                                </label>
                                <p className="pl-1">或拖放图片到此处</p>
                              </div>
                              <p className="text-xs text-gray-500">
                                PNG, JPG, GIF, WEBP 最大 5MB，支持批量选择
                              </p>
                            </div>
                          </div>
                          {uploadProgress > 0 && (
                            <div className="mt-2">
                              <div className="relative pt-1">
                                <div className="flex mb-2 items-center justify-between">
                                  <div>
                                    <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-primary-600 bg-primary-200">
                                      上传中
                                    </span>
                                  </div>
                                  <div className="text-right">
                                    <span className="text-xs font-semibold inline-block text-primary-600">
                                      {uploadProgress}%
                                    </span>
                                  </div>
                                </div>
                                <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-primary-200">
                                  <div style={{ width: `${uploadProgress}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-primary-500"></div>
                                </div>
                              </div>
                            </div>
                          )}
                          {uploadError && (
                            <div className="mt-2 text-sm text-red-600">
                              {uploadError}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-5">
                <div className="flex justify-end">
                  <Link href="/admin/projects" className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    取消
                  </Link>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {submitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        保存中...
                      </>
                    ) : '保存项目'}
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}