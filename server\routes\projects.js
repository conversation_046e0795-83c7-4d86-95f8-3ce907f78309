const express = require('express');
const {
  getProjects,
  getProject,
  createProject,
  updateProject,
  deleteProject,
  getFeaturedProjects,
  getProjectsByCategory
} = require('../controllers/projects');

const router = express.Router();

const { protect, authorize } = require('../middleware/auth');

// 特色项目路由
router.get('/featured', getFeaturedProjects);

// 按分类获取项目
router.get('/category/:categoryId', getProjectsByCategory);

// 基本CRUD路由
router
  .route('/')
  .get(getProjects)
  .post(protect, authorize('admin', 'editor'), createProject);

router
  .route('/:id')
  .get(getProject)
  .put(protect, authorize('admin', 'editor'), updateProject)
  .delete(protect, authorize('admin', 'editor'), deleteProject);

module.exports = router;