const fs = require('fs');
const path = require('path');

// 测试上传目录
const uploadDir = path.join(__dirname, '../uploads');

console.log('测试上传功能...');
console.log('上传目录:', uploadDir);

// 检查目录是否存在
if (fs.existsSync(uploadDir)) {
  console.log('✓ 上传目录存在');
  
  // 检查目录权限
  try {
    fs.accessSync(uploadDir, fs.constants.R_OK | fs.constants.W_OK);
    console.log('✓ 上传目录可读写');
  } catch (err) {
    console.log('✗ 上传目录权限错误:', err.message);
  }
  
  // 列出目录内容
  try {
    const files = fs.readdirSync(uploadDir);
    console.log('目录内容:', files.length, '个文件');
    files.forEach(file => {
      const filePath = path.join(uploadDir, file);
      const stats = fs.statSync(filePath);
      console.log(`  - ${file} (${stats.size} bytes)`);
    });
  } catch (err) {
    console.log('✗ 读取目录失败:', err.message);
  }
} else {
  console.log('✗ 上传目录不存在');
  
  // 尝试创建目录
  try {
    fs.mkdirSync(uploadDir, { recursive: true });
    console.log('✓ 已创建上传目录');
  } catch (err) {
    console.log('✗ 创建目录失败:', err.message);
  }
}

// 检查环境变量
console.log('\n环境变量:');
console.log('MAX_FILE_UPLOAD:', process.env.MAX_FILE_UPLOAD || '未设置');
console.log('NODE_ENV:', process.env.NODE_ENV || '未设置');
console.log('PORT:', process.env.PORT || '未设置');
