const mongoose = require('mongoose');
const pinyinModule = require('pinyin');
const pinyin = pinyinModule.default || pinyinModule.pinyin; // 获取正确的pinyin函数

const CategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, '请提供分类名称'],
    unique: true,
    trim: true,
    maxlength: [50, '分类名称不能超过50个字符']
  },
  slug: {
    type: String,
    unique: true,
    trim: true,
    lowercase: true
  },
  description: {
    type: String,
    maxlength: [500, '描述不能超过500个字符']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// 创建分类slug
CategorySchema.pre('save', function(next) {
  // 只要有name，就生成slug，无论slug是否已存在
  if (this.name) {
    try {
      // 使用pinyin库将中文转换为拼音
      const pinyinArray = pinyin(this.name, {
        style: pinyinModule.STYLE_NORMAL || 0, // 普通风格，不带声调
        heteronym: false           // 禁用多音字模式
      });
      
      // 将二维数组转换为一维数组，然后用连字符连接
      const pinyinResult = pinyinArray.map(item => item[0]).join('-');
      
      // 创建slug - 将拼音转换为URL友好的格式
      this.slug = pinyinResult
        .toLowerCase()
        .replace(/[^\w\s-]/g, '') // 移除非单词/空格/连字符字符
        .replace(/[\s_-]+/g, '-') // 将空格和下划线替换为连字符
        .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符
    } catch (error) {
      console.error('生成拼音slug时出错:', error);
      // 出错时使用时间戳作为slug
      this.slug = Date.now().toString();
    }
    
    // 如果slug为空（可能是因为name只包含特殊字符），使用时间戳
    if (!this.slug) {
      this.slug = Date.now().toString();
    }
  }
  
  next();
});

module.exports = mongoose.model('Category', CategorySchema);