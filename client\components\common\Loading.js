export default function Loading({ size = 'medium', text = '加载中...' }) {
  // 根据size参数确定加载图标的大小
  const sizeClasses = {
    small: 'h-4 w-4 border-2',
    medium: 'h-8 w-8 border-2',
    large: 'h-12 w-12 border-b-2'
  };

  // 根据size参数确定文本大小
  const textClasses = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base'
  };

  return (
    <div className="flex flex-col items-center justify-center py-4">
      <div className={`animate-spin rounded-full ${sizeClasses[size]} border-primary-500 border-t-transparent`}></div>
      {text && <p className={`mt-2 text-gray-500 ${textClasses[size]}`}>{text}</p>}
    </div>
  );
}